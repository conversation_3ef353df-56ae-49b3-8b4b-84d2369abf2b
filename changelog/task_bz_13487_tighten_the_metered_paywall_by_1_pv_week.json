{"affectedProjects": ["bz", "bz-mobile", "india", "money", "newsdesk-tools", "pro", "proto", "widgets-pro-calendar", "widgets-pro-insiders", "@benzinga/root", "bz-e2e", "data-manager-alts", "data-manager-article", "legacy-fission", "pro-e2e", "react-utils-data-hooks-calendar-manager", "react-utils-data-hooks-content-manager", "react-utils-data-hooks-watchlist-manager", "react-utils-user-context", "ui-ads", "ui-alternative-investments", "ui-article", "ui-atomics", "ui-auth", "ui-blocks", "ui-bz-onboarding", "ui-calendars", "ui-charts", "ui-crypto", "ui-entity", "ui-forms", "ui-miller-columns", "ui-money", "ui-navigation", "ui-news", "ui-pro-ui", "ui-product", "ui-quotes", "ui-reviews", "ui-table", "ui-templates", "ui-ticker", "ui-trade-ideas", "ui-ui", "ui-watchlist-ui", "ui-widgets", "utils-analytics", "visualization-analyst-ratings", "visualization-bar-chart", "visualization-guage", "visualization-heatmap", "visualization-iqchart", "visualization-pie-chart", "visualization-plotly", "visualization-sank<PERSON>", "visualization-trading-view-light-weight-chart", "widget-pro-bz-chart", "widget-pro-calendars", "widget-pro-chart", "widget-pro-chat", "widget-pro-details", "widget-pro-gpt", "widget-pro-graph", "widget-pro-insiders", "widget-pro-movers", "widget-pro-newsfeed", "widget-pro-notification", "widget-pro-option-chain", "widget-pro-research", "widget-pro-scanner", "widget-pro-signals", "widget-pro-watchlist", "widget-pro-widget-utils", "widget-scanner", "widget-sensa-market", "widget-ticker-finder", "wnstn-chat"], "description": "tighten the metered paywall by 1 pv week", "epic": null, "issueNumber": "13487", "project": "BZ", "projects": ["bz", "bz-mobile", "india", "money", "newsdesk-tools", "pro", "proto", "widgets-pro-calendar", "widgets-pro-insiders", "@benzinga/root", "bz-e2e", "data-manager-alts", "data-manager-article", "legacy-fission", "pro-e2e", "react-utils-data-hooks-calendar-manager", "react-utils-data-hooks-content-manager", "react-utils-data-hooks-watchlist-manager", "react-utils-user-context", "ui-ads", "ui-alternative-investments", "ui-article", "ui-atomics", "ui-auth", "ui-blocks", "ui-bz-onboarding", "ui-calendars", "ui-charts", "ui-crypto", "ui-entity", "ui-forms", "ui-miller-columns", "ui-money", "ui-navigation", "ui-news", "ui-pro-ui", "ui-product", "ui-quotes", "ui-reviews", "ui-table", "ui-templates", "ui-ticker", "ui-trade-ideas", "ui-ui", "ui-watchlist-ui", "ui-widgets", "utils-analytics", "visualization-analyst-ratings", "visualization-bar-chart", "visualization-guage", "visualization-heatmap", "visualization-iqchart", "visualization-pie-chart", "visualization-plotly", "visualization-sank<PERSON>", "visualization-trading-view-light-weight-chart", "widget-pro-bz-chart", "widget-pro-calendars", "widget-pro-chart", "widget-pro-chat", "widget-pro-details", "widget-pro-gpt", "widget-pro-graph", "widget-pro-insiders", "widget-pro-movers", "widget-pro-newsfeed", "widget-pro-notification", "widget-pro-option-chain", "widget-pro-research", "widget-pro-scanner", "widget-pro-signals", "widget-pro-watchlist", "widget-pro-widget-utils", "widget-scanner", "widget-sensa-market", "widget-ticker-finder", "wnstn-chat"], "type": "task", "updatedAt": "2025-07-08T16:17:43.598Z"}