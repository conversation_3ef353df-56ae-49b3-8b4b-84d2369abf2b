import SlugPageLayout from '../_components/SlugPageLayout';
import { getServerProps, SlugServerProps } from './serverProps';
import React from 'react';
import PageLayout from '../_components/PageLayout';
import { MetaProps, metaV2 } from '@benzinga/seo';

export const generateMetadata = async props => {
  const params = await props.params;
  const { props: serverProps } = (await getServerProps({ params })) as SlugServerProps;

  return await metaV2(serverProps?.metaProps as MetaProps);
};
const SlugPage = async props => {
  const params = await props.params;
  const data = await getServerProps({ params });

  return (
    <PageLayout pageProps={data.props}>
      <SlugPageLayout {...data} />
    </PageLayout>
  );
};
export default SlugPage;
