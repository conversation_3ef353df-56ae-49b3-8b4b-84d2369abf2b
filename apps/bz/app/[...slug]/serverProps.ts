'use server';

import { combineStoryObjectArrays, filterDuplicateArticles, StoryObject } from '@benzinga/advanced-news-manager';
import { SimpleNewsQueryAndOptions } from '@benzinga/internal-news-manager';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';
import { ContentManager, formatTermsSurrogateKeys } from '@benzinga/content-manager';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { generateTargeting, injectBlockInLayout, moneyMetaInfo, PageProps } from '@benzinga/money';
import { safeTimeout, SafeType } from '@benzinga/safe-await';
import { getGlobalSession } from '../../pages/api/session';
import { getTermByPath } from '../../pages/api/term';
import { termMetaInfo } from '../_libs/getSlugPageData';
import { BasicNewsManager } from '@benzinga/basic-news-manager';
import { DateTime } from 'luxon';
import { getTaboolaBlock } from '@benzinga/ads-utils';
import { getRequestInfo } from '../_utils/serverUtils';

export interface SlugServerProps {
  props: PageProps;
}

export const getServerProps = async ({ params }): Promise<SlugServerProps> => {
  let slug = '';
  if (Array.isArray(params.slug)) {
    slug = sanitizeHTML(params.slug.join('/'));
  } else if (typeof params.slug === 'string') {
    slug = sanitizeHTML(params.slug as string);
  }
  const session = getGlobalSession();
  const contentManager = session.getManager(ContentManager);
  const { cookies, headers } = await getRequestInfo();

  const header = new Headers();

  try {
    // Check for Money Page
    const postResponse = await contentManager.getPageWithPath(slug);
    const postData = postResponse.ok;

    if (postData && postData?.success !== false && postData.blocks?.length) {
      if (Array.isArray(postData?.blocks)) {
        postData.blocks = await loadServerSideBlockData(session, postData?.blocks, headers, cookies);
      }

      if (Array.isArray(postData?.sidebar?.blocks)) {
        postData.sidebar.blocks = await loadServerSideBlockData(session, postData.sidebar.blocks, headers, cookies);
      }

      if (postData?.template !== 'channel') {
        const targeting = postData?.template ? generateTargeting(postData, postData?.template) : {};
        const brokerWidgetRes = postData?.layout?.settings?.hide_footer
          ? await contentManager.getWordpressPost(154603)
          : null;

        return {
          props: {
            brokerWidget: brokerWidgetRes?.ok || null,
            headerProps: {
              hideBanner: true,
              hideFooter: !!postData?.layout?.settings?.hide_site_footer,
              hideNavigationBar: !!postData?.layout?.settings?.hide_navigation_bar,
              hideQuoteBar: !!postData?.layout?.settings?.hide_quotes_searchbar,
              hideSearchBar: true,
              isMainDrawerVisible: true,
            },
            metaProps: moneyMetaInfo(postData),
            pageTargeting: targeting,
            post: postData,
            template: postData?.template ?? 'money',
          },
        };
      }
    }

    if (Array.isArray(postData?.sidebar?.blocks)) {
      postData.sidebar.blocks = await loadServerSideBlockData(session, postData.sidebar.blocks, headers, cookies);
    }

    // News Listing Page
    let featuredNewsResponse: SafeType<StoryObject[]> | null = null;
    let newsResponse: SafeType<StoryObject[]> | null = null;
    let featuredNews: StoryObject[] = [];

    const result = await getTermByPath(`${slug}`);

    if (!result?.ok || result?.ok?.response_code === 400) {
      return {
        props: {
          error: 404,
          featuredNews: [],
          news: [],
          topic: '',
        },
      };
    }

    const term = result?.ok?.data && result.ok.data[0];
    const metaProps = postData ? moneyMetaInfo(postData) : termMetaInfo(term);
    let newsQuery: SimpleNewsQueryAndOptions | undefined = {};

    let template = '';

    let pageTargeting = {};

    const featuredNewsLimit = term.tid === '2' ? 3 : 7;

    if (term.vid === '1') {
      template = 'channel';
      const basicNewsManager = await session.getManager(BasicNewsManager);
      const now = DateTime.now();

      featuredNewsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { channels: [138079], primaryChannel: term.tid },
          {
            after: now.minus({ days: 2 }).toFormat('yyyy-MM-dd'),
            excludeAutomated: true,
            limit: featuredNewsLimit,
          },
        ),
        3000,
      );
      newsQuery = {
        channels: [term.tid],
        excludeAutomated: true,
      };
      newsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { channels: newsQuery.channels },
          {
            excludeAutomated: newsQuery.excludeAutomated,
            limit: 20,
          },
        ),
        3000,
      );
    }

    const getTopicByNameRes = await session.getManager(ContentManager).getTopicByName(term?.name);

    const isIsraelPage = term?.tid === '21365';

    if (term.vid === '3') {
      template = 'topic';
      const basicNewsManager = await session.getManager(BasicNewsManager);
      const now = DateTime.now();
      const newsType = term.tid === '938584' ? 'benzinga_stockinsiderreport' : 'story';

      featuredNewsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { tags: [term.tid] },
          {
            after: now.minus({ days: 2 }).toFormat('yyyy-MM-dd'),
            displayOutput: 'abstract',
            excludeAutomated: true,
            limit: featuredNewsLimit,
            type: newsType,
          },
        ),
        3000,
      );

      newsQuery = {
        displayOutput: 'abstract',
        headlines: isIsraelPage ? 'include' : undefined,
        tags: [term.tid],
        type: newsType,
      };

      newsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { tags: [term.tid] },
          {
            displayOutput: newsQuery.displayOutput,
            headlines: newsQuery.headlines,
            limit: 30, //this is BAD // This parameter is not accurate often return less articles,
            type: newsQuery.type,
          },
        ),
        3000,
      );

      if (
        !getTopicByNameRes?.ok?.layout_meta?.force_index &&
        Array.isArray(newsResponse?.ok) &&
        newsResponse.ok.length < 15
      ) {
        metaProps.robots = 'noindex, nofollow';
      }
    }

    // TODO: Fetch related WP Page, use SEO Meta Data and Layout

    // const sidebarRes = await safeAwait(getMoneySidebar(96438));
    if (Array.isArray(featuredNewsResponse?.ok)) {
      featuredNews = featuredNewsResponse.ok;
    }

    let news = newsResponse?.ok ? filterDuplicateArticles(newsResponse?.ok) : [];

    const featuredNewsIds = featuredNews.map(node => node.id);

    if (featuredNewsLimit - featuredNews.length) {
      news = news.filter(node => !featuredNewsIds.includes(node.id));
      featuredNews = featuredNews.concat(news.splice(0, featuredNewsLimit - featuredNews.length));
    }

    if (news.length > 0) {
      news = news.filter(node => !featuredNewsIds.includes(node.id));
    }

    if (featuredNews.length < 3) {
      news = combineStoryObjectArrays(news, featuredNews);
      featuredNews = [];
    }

    if (Array.isArray(getTopicByNameRes?.ok?.above_content?.blocks)) {
      getTopicByNameRes.ok.above_content.blocks = await loadServerSideBlockData(
        session,
        getTopicByNameRes.ok.above_content.blocks,
        headers,
      );
    }

    if (featuredNews[0]) {
      metaProps.dateCreated = featuredNews[0].updated;
      metaProps.dateUpdated = featuredNews[0].updated;
    }

    if (term) {
      header.set('Surrogate-Key', formatTermsSurrogateKeys(term));
    }

    if (
      ['topic', 'channel'].includes(template) &&
      (featuredNewsResponse?.err || newsResponse?.err) &&
      process.env.NODE_ENV !== 'development'
    ) {
      return {
        props: {
          error: 503,
          featuredNews: [],
          news: [],
          slug,
          topic: '',
        },
      };
    }

    news = isIsraelPage ? combineStoryObjectArrays(news, featuredNews) : news;

    if (!news?.length && !getTopicByNameRes?.ok && !postData && !featuredNews?.length) {
      return {
        props: {
          error: 404,
          featuredNews: [],
          news: [],
          topic: '',
        },
      };
    }

    let layout = postData ?? getTopicByNameRes.ok ?? null;

    if (template === 'channel' || template === 'topic') {
      if (!layout) {
        layout = {
          below_main_content: null,
          header: null,
          in_content: null,
          sidebar: null,
        };
      }

      if (postData && Array.isArray(postData?.in_content?.blocks)) {
        postData.in_content.blocks = await loadServerSideBlockData(
          session,
          postData?.in_content?.blocks,
          headers,
          cookies,
        );
      }

      injectBlockInLayout(layout, template === 'channel' ? 'below_main_content' : 'in_content', getTaboolaBlock());

      pageTargeting = { BZ_PTYPE: template };
      if (term) {
        if (template === 'channel') {
          pageTargeting['BZ_CHANNEL'] = [term.name, term.tid];
        } else if (template === 'topic') {
          pageTargeting['BZ_TAG'] = [term.name, term.tid];
        }
      }
    }

    const brokerWidgetRes = postData?.layout?.settings?.hide_footer
      ? await contentManager.getWordpressPost(154603)
      : null;

    return {
      props: {
        brokerWidget: brokerWidgetRes?.ok || null,
        featuredNews,
        layout,
        metaProps,
        news,
        newsQuery,
        pageTargeting,
        post: postData ?? null,
        slug,
        template: template,
        term: term,
      },
    };
  } catch (error) {
    console.error('Error with [...slug] page data', error);
    return {
      props: {
        error: 503,
        featuredNews: [],
        news: [],
        slug,
        topic: '',
      },
    };
  }
};
