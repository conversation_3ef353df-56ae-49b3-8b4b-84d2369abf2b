import React from 'react';
import Image from 'next/image';
import styled from '@benzinga/themetron';
import { Impression } from '@benzinga/analytics';
import { useWindowSize } from '@benzinga/hooks';
import { BenzingaLogo, BenzingaProLogo } from '@benzinga/logos-ui';
import { Close } from '@benzinga/themed-icons';

// Change banner version here
import styles from './rotatingBanners.module.scss';
import { BzEdgeLogo, bzEdgeVariations } from './bzEdge';
import { proWebinarVariations, July4ProIconSvg } from './proWebinar';
import { usePermission } from '@benzinga/user-context';
import { Inter } from 'next/font/google';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  weight: ['400', '500', '600', '700'],
});

type Variation = {
  button: string;
  buttonMobile: string;
  description: string;
  logoType: string;
  title: string;
  url: string;
  variant: string;
  expiryDate?: string;
};

const variations: Variation[] = [...bzEdgeVariations, ...proWebinarVariations];

interface Props {
  close: () => void;
  fallback?: React.ReactNode;
}

const localStorageKey = 'topbdata';

export const RotatingBanner: React.FC<Props> = ({ close, fallback }) => {
  const [bannerIndex, setBannerIndex] = React.useState<number | null>(null);
  const hasBzEdge = usePermission('com/read', 'unlimited-calendars');
  const windowWidth = useWindowSize().width;

  const getBannerIndex = () => {
    const bannerInfo = localStorage.getItem(localStorageKey);
    if (bannerInfo) {
      const { index } = JSON.parse(bannerInfo);
      return (parseInt(index) + 1) % variations.length;
    } else {
      return Math.floor(Math.random() * variations.length);
    }
  };

  const hideBanner = e => {
    e.preventDefault();
    localStorage.setItem('hideRotatingBanner', 'true');
    if (close) {
      close();
    }
  };

  React.useEffect(() => {
    if (bannerIndex === null) {
      let idx = getBannerIndex();
      let skipEdge = JSON.parse(localStorage.getItem(localStorageKey) || '{}').skip;
      if ((skipEdge || hasBzEdge) && idx < 2) {
        idx = 3;
        skipEdge = true;
      }
      const variation = variations[idx];
      // if (variation?.startDate && new Date(variation.startDate) > new Date()) {
      //   idx = (idx + 1) % variations.length;
      // }
      if (variation?.expiryDate && new Date(variation.expiryDate) < new Date()) {
        idx = (idx + 1) % variations.length;
      }
      setBannerIndex(idx);

      localStorage.setItem(
        localStorageKey,
        JSON.stringify({
          index: idx,
          skip: false,
        }),
      );
    }
  }, [bannerIndex, hasBzEdge]);

  const getLogo = type => {
    switch (type) {
      case 'pro-light':
        return <BenzingaProLogo variant="default" />;
      case 'pro-dark':
        return <BenzingaProLogo variant="blue" />;
      case 'bz-edge':
        return <BzEdgeLogo />;
      case 'bz-dark':
        return <BenzingaLogo variant="dark" />;
      case 'bz-light':
        return <BenzingaLogo variant="light" />;
      default:
        return null;
    }
  };

  if (bannerIndex !== null) {
    return (
      <Impression
        campaign_id={variations[bannerIndex].variant}
        tag={variations[bannerIndex].variant}
        unit_type={'Rotating Banner'}
      >
        <Wrapper
          className={`banner-wrapper ${styles[variations[bannerIndex].variant]} ${inter.className}`}
          href={variations[bannerIndex].url}
          target="_blank"
        >
          <div className={`banner-bg start ${styles['banner-bg-start']}`}></div>
          {variations[bannerIndex].variant !== 'earnings-webinar-v1' && (
            <Image
              alt="Promo Banner"
              className={`absolute top-0 ${styles['banner-image']}`}
              fill
              priority={true}
              src={`/next-assets/images/banners/rotation/${variations[bannerIndex].variant}.png`}
              style={{ objectFit: 'cover' }}
            />
          )}

          <div className={`banner ${styles.banner}`}>
            <div className={`banner-content ${styles['banner-content']}`}>
              {variations[bannerIndex].logoType && (
                <div className={`logo-wrapper desktop ${styles['logo-wrapper']}`}>
                  {getLogo(variations[bannerIndex].logoType)}
                </div>
              )}
              <div className={styles['content']}>
                <h3>
                  {variations[bannerIndex].title}

                  {(variations[bannerIndex].variant === 'pro-sale-v4-july4-2' ||
                    variations[bannerIndex].variant === 'pro-sale-v4-july4-3') && (
                    <div className={styles['percent-icon']}>
                      <July4ProIconSvg />
                    </div>
                  )}
                </h3>
                <p
                  className="description"
                  dangerouslySetInnerHTML={{ __html: variations[bannerIndex].description }}
                ></p>
              </div>
            </div>
            <div className={`button-wrapper ${styles['button-wrapper']}`}>
              {variations[bannerIndex].logoType && (
                <div className="logo-wrapper mobile">{getLogo(variations[bannerIndex].logoType)}</div>
              )}
              {variations[bannerIndex].button && (
                <button className={`banner-button ${styles['banner-button']}`} name="Close Top Banner Button">
                  {windowWidth < 800 && variations[bannerIndex].buttonMobile
                    ? variations[bannerIndex].buttonMobile
                    : variations[bannerIndex].button}
                </button>
              )}
              <div className={`banner-button-bg ${styles['banner-button-bg']}`}></div>
            </div>
          </div>
          {/* <div className={`banner-bg end ${styles['banner-bg-end']}`}></div> */}
          {variations[bannerIndex].variant === 'earnings-webinar-v1' && (
            <Image
              alt="Webinar Banner"
              className={`banner-bg end ${styles['banner-bg-end']}`}
              height={80}
              priority={true}
              src={`/next-assets/images/banners/${variations[bannerIndex].variant}.png`}
              style={{ objectFit: 'cover' }}
              width={944}
            />
          )}
          <button className="close-button" name="Close Top Banner Button" onClick={e => hideBanner(e)}>
            <Close fill="white" height="16px" width="16px" />
          </button>
        </Wrapper>
      </Impression>
    );
  } else {
    return fallback ?? null;
  }
};

export default RotatingBanner;

const Wrapper = styled.a`
  &.banner-wrapper {
    height: 80px;
    width: 100%;
    display: block;
    position: relative;
    overflow: hidden;

    // Base Styles
    .banner-bg {
      position: absolute;
      top: 0;
      height: 100%;
      &.start {
        left: 0;
      }
      &.end {
        right: 0;
      }
    }

    .banner {
      margin: 0 auto;
      max-width: 1400px;
      height: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      color: white;
      position: relative;

      .banner-content {
        z-index: 30;
        display: flex;
      }

      .button-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .logo-wrapper {
        z-index: 100;

        &.mobile {
          display: none;
        }
      }

      p {
        margin: 0;
      }

      .banner-button {
        color: white;
        line-height: 1rem;
        font-weight: ${({ theme }) => theme.fontWeight.bold};
        box-shadow: ${({ theme }) => theme.shadow.sm};
        border-radius: ${({ theme }) => theme.borderRadius.default};
        z-index: 30;
        padding: 12px 36px;
        transition:
          0.25s background-color ease-in-out,
          0.25s color ease-in-out,
          0.25s border-color ease-in-out;
      }
    }

    .close-button {
      position: absolute;
      right: 0;
      top: 0;
      padding: 4px;
      background-color: rgba(255, 255, 255, 0.5);
      z-index: 30;

      svg {
        fill: black;
      }
      &:hover {
        svg {
          fill: #2e79f6;
        }
      }
    }

    @media (max-width: 1440px) {
      .banner {
        padding: 0 1rem;
      }
    }

    @media (max-width: 1100px) {
      .banner {
        .banner-button {
          padding: 8px 30px;
        }
      }
    }
    @media (max-width: 800px) {
      height: 80px;

      .banner {
        padding: 0 0.5rem;
        gap: 0.5rem;
        .banner-button {
          padding: 8px 12px;
        }
      }
    }
  }
`;
