import React, { memo } from 'react';
import { DateTime } from 'luxon';
import styled from '@benzinga/themetron';
import { appEnvironment } from '@benzinga/utils';
import { rgba } from 'polished';

export interface AuthorContentProps {
  created: string;
  isDraft?: boolean;
  readTime: string;
}

export const DateContent: React.FC<AuthorContentProps> = memo(({ created, isDraft, readTime }) => {
  const articleCreationTime = created
    ? DateTime.fromISO(created, { setZone: true })
        .setZone(appEnvironment().config().zone)
        .toFormat('LLLL d, yyyy h:mm a')
    : null;

  return (
    <Container className="article-date-wrap text-sm">
      {isDraft && <span className="draft-badge">Draft</span>}{' '}
      <span className="article-date text-white">{articleCreationTime}</span>
      {readTime && <span className="article-read-time ml-2 inline-block rounded"> {readTime} </span>}
    </Container>
  );
});

const Container = styled.div`
  &.article-date-wrap {
    color: ${({ theme }) => rgba(theme.colorPalette.gray500, 0.8)};
  }
  .article-read-time {
    background-color: #1f334b;
    color: ${({ theme }) => theme.colorPalette.gray200};
    padding: 0.15rem 0.5rem;
  }

  .draft-badge {
    background-color: ${({ theme }) => theme.colorPalette.blue200};
    padding: 2px 10px;
    border-radius: ${({ theme }) => theme.borderRadius.default};
  }
`;
